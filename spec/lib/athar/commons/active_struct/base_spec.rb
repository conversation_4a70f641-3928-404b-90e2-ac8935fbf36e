require 'active_model'
require 'active_support'
require 'active_support/core_ext'
require 'securerandom'

RSpec.describe Athar::Commons::ActiveStruct::Base do
  describe 'ID attribute configuration' do
    context 'with default configuration' do
      let(:model_class) do
        Class.new(described_class) do
          attribute :name, :string
        end
      end

      it 'has ID attribute enabled by default' do
        expect(model_class.has_id_attribute).to be true
        expect(model_class.auto_generate_id).to be true
        expect(model_class.id_enabled?).to be true
      end

      it 'creates instances with auto-generated IDs' do
        instance = model_class.new(name: 'Test')
        expect(instance).to respond_to(:id)
        expect(instance.id).to be_present
        expect(instance.persisted?).to be true
      end

      it 'supports manual ID setting' do
        instance = model_class.new(id: 'custom-123', name: 'Test')
        expect(instance.id).to eq('custom-123')
        expect(instance.persisted?).to be true
      end
    end

    context 'with without_id configuration' do
      let(:model_class) do
        Class.new(described_class) do
          without_id
          attribute :name, :string
        end
      end

      it 'disables ID attribute completely' do
        expect(model_class.has_id_attribute).to be false
        expect(model_class.auto_generate_id).to be false
        expect(model_class.id_enabled?).to be false
      end

      it 'creates instances without ID attribute' do
        instance = model_class.new(name: 'Test')
        expect(instance).not_to respond_to(:id)
        expect(instance.persisted?).to be false
      end

      it 'excludes ID from attribute_types' do
        expect(model_class.attribute_types.keys).not_to include('id')
        expect(model_class.attribute_types.keys).to include('name')
      end

      it 'ignores ID in initialization' do
        instance = model_class.new(id: 'ignored', name: 'Test')
        expect(instance).not_to respond_to(:id)
        expect(instance.persisted?).to be false
      end
    end

    context 'with manual ID configuration' do
      let(:model_class) do
        Class.new(described_class) do
          with_id(auto_generate: false)
          attribute :name, :string
        end
      end

      it 'has ID attribute but no auto-generation' do
        expect(model_class.has_id_attribute).to be true
        expect(model_class.auto_generate_id).to be false
        expect(model_class.id_enabled?).to be true
      end

      it 'creates instances without auto-generated IDs' do
        instance = model_class.new(name: 'Test')
        expect(instance).to respond_to(:id)
        expect(instance.id).to be_blank
        expect(instance.persisted?).to be false
      end

      it 'accepts manual ID setting' do
        instance = model_class.new(id: 'manual-123', name: 'Test')
        expect(instance.id).to eq('manual-123')
        expect(instance.persisted?).to be true
      end
    end

    context 'inheritance scenarios' do
      let(:base_command_class) do
        Class.new(described_class) do
          without_id
          attribute :name, :string
        end
      end

      let(:specific_command_class) do
        Class.new(base_command_class) do
          attribute :command_type, :string
        end
      end

      it 'inherits without_id configuration correctly' do
        expect(base_command_class.has_id_attribute).to be false
        expect(specific_command_class.has_id_attribute).to be false
      end

      it 'creates instances without ID in inheritance chain' do
        base_instance = base_command_class.new(name: 'Base Test')
        specific_instance = specific_command_class.new(name: 'Specific Test', command_type: 'voice')

        expect(base_instance).not_to respond_to(:id)
        expect(specific_instance).not_to respond_to(:id)
        expect(base_instance.persisted?).to be false
        expect(specific_instance.persisted?).to be false
      end

      it 'excludes ID from attribute_types in inheritance chain' do
        expect(base_command_class.attribute_types.keys).to eq(['name'])
        expect(specific_command_class.attribute_types.keys).to eq(['name', 'command_type'])
      end
    end
  end

  describe 'form builder support methods' do
    context 'with ID enabled' do
      let(:model_class) do
        Class.new(described_class) do
          attribute :name, :string
        end
      end

      it 'provides correct form builder methods when persisted' do
        instance = model_class.new(name: 'Test')
        expect(instance.to_key).to eq([instance.id])
        expect(instance.to_param).to eq(instance.id)
        expect(instance.new_record?).to be false
      end

      it 'provides correct form builder methods when not persisted' do
        instance = model_class.new(id: nil, name: 'Test')
        expect(instance.to_key).to be_nil
        expect(instance.to_param).to be_nil
        expect(instance.new_record?).to be true
      end
    end

    context 'with ID disabled' do
      let(:model_class) do
        Class.new(described_class) do
          without_id
          attribute :name, :string
        end
      end

      it 'provides correct form builder methods' do
        instance = model_class.new(name: 'Test')
        expect(instance.to_key).to be_nil
        expect(instance.to_param).to be_nil
        expect(instance.persisted?).to be false
        expect(instance.new_record?).to be true
      end
    end
  end

  describe 'JSON serialization' do
    context 'with ID enabled' do
      let(:model_class) do
        Class.new(described_class) do
          attribute :name, :string
        end
      end

      it 'includes ID in JSON output' do
        instance = model_class.new(name: 'Test')
        json = instance.as_json
        expect(json).to have_key('id')
        expect(json['id']).to be_present
        expect(json['name']).to eq('Test')
      end
    end

    context 'with ID disabled' do
      let(:model_class) do
        Class.new(described_class) do
          without_id
          attribute :name, :string
        end
      end

      it 'excludes ID from JSON output' do
        instance = model_class.new(name: 'Test')
        json = instance.as_json
        expect(json).not_to have_key('id')
        expect(json['name']).to eq('Test')
      end
    end
  end
end
