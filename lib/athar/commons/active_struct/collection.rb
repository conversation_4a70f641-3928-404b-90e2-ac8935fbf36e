# frozen_string_literal: true

module Athar
  module Commons
    module ActiveStruct
      # Collection module for groups of structured data objects
      # Implements Enumerable and provides methods for finding, filtering, and manipulating collections
      module Collection
        extend ActiveSupport::Concern

        included do
          include Enumerable
          attr_reader :items, :owner_id, :owner_type
          attr_accessor :changed
        end

        # === Initialization ===
        def initialize(owner_id = nil, owner_type = nil, items = [])
          @owner_id = owner_id
          @owner_type = owner_type
          @items = []
          @offset = 0
          @limit = nil
          @changed = false
          Array(items).each { |item| self << item }
          @changed = false
        end

        # === Enumerable Implementation ===
        def each(&block)
          result = @items
          result = result[@offset.to_i..] if @offset.to_i > 0
          result = result[0...@limit.to_i] if @limit.to_i > 0
          result.each(&block)
        end

        # === Collection Manipulation ===
        def <<(item)
          item_obj = if item.is_a?(item_class)
                       item
                     elsif item.is_a?(Array) && item.first.is_a?(Hash)
                       item_class.new(item.first.merge(owner_attributes))
                     elsif item.is_a?(Hash)
                       item_class.new(item.merge(owner_attributes))
                     elsif item.is_a?(ActionController::Parameters)
                       item_class.new(item.to_unsafe_hash.merge(owner_attributes))
                     else
                       item_class.new(owner_attributes)
                     end
          @items << item_obj
          @changed = true
          self
        end

        def clear
          old_size = @items.size
          @items = []
          @changed = old_size > 0
          self
        end

        # === Query Methods ===
        def find_by_id(id)
          @items.find { |item| item.respond_to?(:id) && item.id.to_s == id.to_s }
        end

        # Override Enumerable's find to support both behaviors
        def find(id_or_block = nil, &block)
          # If block is given, use Enumerable's find behavior
          if block_given?
            each.find(&block)
          elsif id_or_block.nil?
            # If no arguments, return enumerator (Enumerable behavior)
            each.find
          else
            # If argument is given and no block, treat as ID lookup
            find_by_id(id_or_block)
          end
        end

        def where(conditions = {})
          result = @items
          conditions.each { |key, value| result = result.select { |item| item_matches?(item, key, value) } }
          self.class.new(owner_id, owner_type, result)
        end

        def find_by(conditions = {})
          where(conditions).first
        end

        def first
          to_a.first
        end

        def last
          to_a.last
        end

        def to_a
          result = @items
          result = result[@offset.to_i..] if @offset.to_i > 0
          result = result[0...@limit.to_i] if @limit.to_i > 0
          result.to_a
        end

        def empty?
          to_a.empty?
        end

        def present?
          !empty?
        end

        def size
          @items.size
        end

        # === ID Management ===
        def ids
          map { |item| item.respond_to?(:id) ? item.id : nil }.compact
        end

        def ids=(ids)
          # Only allow setting IDs if the item class supports IDs
          return unless item_class.respond_to?(:has_id_attribute) && item_class.has_id_attribute

          old_ids = self.ids
          clear
          Array(ids).each { |id| self << { id: id } if id.present? }
          @changed = (old_ids.sort != Array(ids).map(&:to_s).sort)
        end

        # === Pagination & Limiting ===
        def offset(value)
          @offset = value.to_i
          self
        end

        def limit(value)
          @limit = value.to_i
          self
        end

        def page(number)
          PaginationAdapter.new(self, number.to_i, @per_page || 25)
        end

        def per(size)
          @per_page = size.to_i
          self
        end

        # === Serialization ===
        def as_json(_options = nil)
          to_a.map(&:as_json)
        end

        # === Sorting ===
        def order(field_or_hash)
          if field_or_hash.is_a?(Hash)
            field, direction = field_or_hash.first
          else
            field = field_or_hash.to_s
            direction = :asc
          end
          if field.start_with?('-')
            field = field[1..]
            direction = :desc
          end
          sorted = sort_by { |item| item.send(field) if item.respond_to?(field) }
          sorted = sorted.reverse if direction.to_s.downcase == 'desc'
          self.class.new(owner_id, owner_type, sorted)
        end

        # === Miscellaneous ===
        def model
          item_class
        end

        def ransack(params = {})
          RansackAdapter.new(self, params)
        end

        def owner_attributes
          attrs = {}
          attrs["#{owner_type.to_s.underscore}_id".to_sym] = owner_id if owner_id && owner_type
          attrs
        end

        def changed?
          @changed
        end

        def item_class
          raise NotImplementedError, "Collection classes must implement item_class method"
        end

        # === Class Methods ===
        def self.from_api_params(params, owner_id = nil, owner_type = nil)
          collection = new(owner_id, owner_type)
          Array(params).each { |param| collection << param }
          collection
        end

        module ClassMethods
          def collection_item_class(klass)
            define_method(:item_class) { klass }
          end
        end

        private

        def item_matches?(item, key, value)
          if key.to_s.include?('_')
            field, operator = key.to_s.split('_', 2)
            case operator
            when 'eq' then item.send(field) == value if item.respond_to?(field)
            when 'not_eq' then item.send(field) != value if item.respond_to?(field)
            when 'in' then Array(value).include?(item.send(field)) if item.respond_to?(field)
            when 'not_in' then !Array(value).include?(item.send(field)) if item.respond_to?(field)
            when 'matches', 'like' then item.send(field).to_s.include?(value.to_s) if item.respond_to?(field)
            when 'gt' then item.send(field) > value if item.respond_to?(field)
            when 'lt' then item.send(field) < value if item.respond_to?(field)
            when 'gteq' then item.send(field) >= value if item.respond_to?(field)
            when 'lteq' then item.send(field) <= value if item.respond_to?(field)
            else item.send(key) == value if item.respond_to?(key)
            end
          else
            item.send(key) == value if item.respond_to?(key)
          end
        end
      end

      # === Ransack Adapter ===
      class RansackAdapter
        attr_reader :collection, :params

        def initialize(collection, params)
          @collection = collection
          @params = params
          @sorts = params.delete(:sorts)
        end

        def result
          filtered_collection = filter_collection
          sorted_collection = sort_collection(filtered_collection)
          @collection.class.new(@collection.owner_id, @collection.owner_type, sorted_collection)
        end

        def filter_collection
          filtered_collection = @collection
          @params.each do |key, value|
            next if value.blank?
            if key.to_s.include?('_')
              field, operator = key.to_s.split('_', 2)
              next unless %w[eq not_eq in not_in matches like gt lt gteq lteq].include?(operator)
              filtered_collection = filtered_collection.select do |item|
                case operator
                when 'eq' then item.send(field) == value if item.respond_to?(field)
                when 'not_eq' then item.send(field) != value if item.respond_to?(field)
                when 'in' then Array(value).include?(item.send(field)) if item.respond_to?(field)
                when 'not_in' then !Array(value).include?(item.send(field)) if item.respond_to?(field)
                when 'matches', 'like' then item.send(field).to_s.include?(value.to_s) if item.respond_to?(field)
                when 'gt' then item.send(field) > value if item.respond_to?(field)
                when 'lt' then item.send(field) < value if item.respond_to?(field)
                when 'gteq' then item.send(field) >= value if item.respond_to?(field)
                when 'lteq' then item.send(field) <= value if item.respond_to?(field)
                end
              end
            else
              filtered_collection = filtered_collection.select { |item| item.send(key) == value if item.respond_to?(key) }
            end
          end
          filtered_collection
        end

        def sort_collection(collection)
          return collection unless @sorts.present?
          sorted = collection
          Array(@sorts).each do |sort_str|
            field, direction = sort_str.split(' ')
            direction = direction.to_s.downcase == 'desc' ? :desc : :asc
            sorted = sorted.sort_by { |item| item.send(field) if item.respond_to?(field) }
            sorted = sorted.reverse if direction == :desc
          end
          sorted
        end

        def sorts=(value)
          @sorts = value
        end

        def sorts
          @sorts
        end

        def method_missing(method, *args, &block)
          @collection.respond_to?(method) ? @collection.send(method, *args, &block) : super
        end

        def respond_to_missing?(method, include_private = false)
          @collection.respond_to?(method, include_private) || super
        end
      end

      # === Pagination Adapter ===
      class PaginationAdapter
        include Enumerable
        attr_reader :collection, :current_page, :per_page

        def initialize(collection, page, per_page = 25)
          @collection = collection
          @current_page = [ page.to_i, 1 ].max
          @per_page = [ per_page.to_i, 1 ].max
        end

        def total_count
          @collection.size
        end

        def total_pages
          (total_count.to_f / per_page).ceil
        end

        def offset
          (current_page - 1) * per_page
        end

        def limit
          per_page
        end

        def to_a
          @collection.to_a[offset, limit] || []
        end

        def each(&block)
          to_a.each(&block)
        end

        def method_missing(method, *args, &block)
          @collection.respond_to?(method) ? @collection.send(method, *args, &block) : super
        end

        def respond_to_missing?(method, include_private = false)
          @collection.respond_to?(method, include_private) || super
        end
      end
    end
  end
end
